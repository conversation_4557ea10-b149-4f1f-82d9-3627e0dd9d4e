# Magentic-UI 启动脚本
# PowerShell script to start Magentic-UI with correct configuration

Write-Host "🚀 启动 Magentic-UI..." -ForegroundColor Green
Write-Host "=" * 50

# 设置环境变量
Write-Host "📝 设置环境变量..." -ForegroundColor Yellow
$env:OPENAI_API_KEY = "hk-a81cro100001220955e04b42c93c04f8e9b16f62aa15c8d8"
$env:OPENAI_BASE_URL = "https://api.openai-hk.com"

# 验证环境变量
Write-Host "✅ API Key: $($env:OPENAI_API_KEY.Substring(0,10))..." -ForegroundColor Green
Write-Host "✅ Base URL: $env:OPENAI_BASE_URL" -ForegroundColor Green

# 启动 Magentic-UI
Write-Host "🌐 启动 Web 服务器..." -ForegroundColor Yellow
Write-Host "访问地址: http://127.0.0.1:8082" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Red
Write-Host ""

# 启动服务
magentic-ui --config ./simple_config.yaml --port 8082
