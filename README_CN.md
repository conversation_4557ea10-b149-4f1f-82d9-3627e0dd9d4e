<div align="center">
<img src="docs/img/magui-readme-logo.png" alt="Magentic-UI Logo">


_在您保持控制的同时自动化您的网络任务_

[![image](https://img.shields.io/pypi/v/magentic_ui.svg)](https://pypi.python.org/pypi/magentic_ui)
[![image](https://img.shields.io/pypi/l/magentic_ui.svg)](https://pypi.python.org/pypi/magentic_ui)
![Python Versions](https://img.shields.io/badge/python-3.10%20%7C%203.11%20%7C%203.12%20%7C%203.13-blue)

</div>

---

Magentic-UI 是一个**研究原型**，它是一个以人为中心的界面，由多智能体系统驱动，可以浏览和执行网络操作、生成和执行代码，以及生成和分析文件。

  https://github.com/user-attachments/assets/7975fc26-1a18-4acb-8bf9-321171eeade7



以下是如何开始使用 Magentic-UI：

> **注意**：在安装之前，请仔细阅读[先决条件](#先决条件)。Magentic-UI 需要 Docker 才能运行，如果您使用的是 Windows，则需要 WSL2。我们建议使用 [uv](https://docs.astral.sh/uv/getting-started/installation/) 进行更快的安装。如果您使用的是 Mac 或 Linux，可以跳过 WSL2 步骤。

```bash
python3 -m venv .venv
source .venv/bin/activate
pip install magentic-ui --upgrade
# export OPENAI_API_KEY=<YOUR API KEY>
magentic-ui --port 8081
```
如果您的端口是 8081，您可以在 <http://localhost:8081> 访问 Magentic-UI。


如果您无法设置 Docker，您可以运行 Magentic-UI 的有限版本，该版本无法执行代码、导航文件或在界面中显示浏览器，使用以下命令：

```bash
magentic-ui --run-without-docker --port 8081
```

您也可以在命令行界面中运行 Magentic-UI：
```bash
magentic-cli --work-dir PATH/TO/STORE/DATA
```

要使用 Azure 模型或 Ollama，请安装可选依赖项：
```bash
# 对于 Azure
pip install magentic-ui[azure] 
# 对于 Ollama
pip install magentic-ui[ollama]
```

有关安装的更多详细信息，请阅读 <a href="#安装">🛠️ 安装</a> 部分。对于常见的安装问题及其解决方案，请参考[故障排除文档](TROUBLESHOOTING.md)。


## 快速导航：
<p align="center">
  <a href="#工作原理">🟪 工作原理</a> &nbsp;|&nbsp;
  <a href="#安装">🛠️ 安装</a> &nbsp;|&nbsp;
  <a href="#故障排除">⚠️ 故障排除</a> &nbsp;|&nbsp; 
  <a href="#贡献">🤝 贡献</a> &nbsp;|&nbsp;
  <a href="#许可证">📄 许可证</a>
</p>

---

## 🟪 工作原理
<p align="center">
  <img src="./docs/img/magenticui_running.png" alt="Magentic-UI" height="400">
</p>

Magentic-UI 特别适用于需要在网络上执行操作的网络任务（例如，填写表单、定制食物订单）、深度导航搜索引擎未索引的网站（例如，筛选航班、从个人网站查找链接）或需要网络导航和代码执行的任务（例如，从在线数据生成图表）。

Magentic-UI 的界面如上面的截图所示，由两个面板组成。左侧面板是会话导航器，用户可以在其中创建新会话来解决新任务、在会话之间切换，并通过会话状态指示器检查会话进度（🔴 需要输入，✅ 任务完成，↺ 任务进行中）。

右侧面板显示所选的会话。这是您可以向 Magentic-UI 输入查询以及文本和图像附件的地方，并观察详细的任务进度以及与智能体交互。会话显示本身分为两个面板：左侧是 Magentic-UI 呈现计划、任务进度并请求操作批准的地方，右侧是浏览器视图，您可以在其中实时查看网络智能体操作并与浏览器交互。最后，在会话显示的顶部有一个进度条，随着 Magentic-UI 的进展而更新。


下面的示例显示了用户与 Magentic-UI 的逐步交互：

<!-- Screenshots -->
<p align="center">
  <img src="docs/img/magui-landing.png" alt="Magentic-UI Landing" width="45%" style="margin:10px;">
  <img src="docs/img/magui-coplanning.png" alt="Co-Planning UI" width="45%" style="margin:10px;">
  <img src="docs/img/magui-cotasking.png" alt="Co-Tasking UI" width="45%" style="margin:10px;">
  <img src="docs/img/magui-actionguard.png" alt="Action Guard UI" width="45%" style="margin:10px;">
</p>


Magentic-UI 与其他浏览器使用产品的区别在于其透明且可控的界面，允许高效的人机协作参与。Magentic-UI 使用 [AutoGen](https://github.com/microsoft/autogen) 构建，提供了一个研究人机交互和实验网络智能体的平台。主要功能包括：

- 🧑‍🤝‍🧑 **协作规划**：使用聊天和计划编辑器协作创建和批准逐步计划。
- 🤝 **协作任务**：直接通过网络浏览器或聊天中断和指导任务执行。Magentic-UI 也可以在需要时请求澄清和帮助。
- 🛡️ **操作保护**：敏感操作只有在用户明确批准后才会执行。
- 🧠 **计划学习和检索**：从以前的运行中学习以改进未来的任务自动化，并将它们保存在计划库中。在未来的任务中自动或手动检索保存的计划。
- 🔀 **并行任务执行**：您可以并行运行多个任务，会话状态指示器会让您知道 Magentic-UI 何时需要您的输入或已完成任务。

<div align="center">
  <a href="https://www.youtube.com/watch?v=wOs-5SR8xOc" target="_blank">
    <img src="https://img.youtube.com/vi/wOs-5SR8xOc/maxresdefault.jpg" alt="Watch the demo video" width="600"/>
  </a>
  <br>
  ▶️ <em> 点击观看视频，了解更多关于 Magentic-UI 的信息 </em>
</div>

### ℹ️ 智能体工作流

Magentic-UI 的底层系统是一个专业智能体团队，改编自 AutoGen 的 Magentic-One 系统，如下图所示。

<p align="center">
  <img src="./docs/img/magenticui.jpg" alt="Magentic-UI" height="400">
</p>

智能体协同工作创建一个模块化系统：

- 🧑‍💼 **协调器** 是主导智能体，由大型语言模型（LLM）驱动，与用户进行协作规划，决定何时向用户请求反馈，并将子任务委托给其余智能体完成。
- 🌐 **网络冲浪者** 是一个配备网络浏览器的 LLM 智能体，它可以控制浏览器。根据协调器的请求，它可以在多轮中点击、输入、滚动和访问页面，以完成协调器的请求。这个智能体在可以执行的操作方面（标签管理、选择选项、文件上传、多模态查询）相比 AutoGen 的 ``MultimodalWebSurfer`` 有显著改进。
要了解更多关于如何构建这个智能体的信息，请跟随这个[教程：从零开始构建浏览器使用智能体以及使用 Magentic-UI](docs/tutorials/web_agent_tutorial_full.ipynb)。
- 💻 **编码器** 是一个配备 Docker 代码执行容器的 LLM 智能体。它可以编写和执行 Python 和 shell 命令，并向协调器提供响应。
- 📁 **文件冲浪者** 是一个配备 Docker 代码执行容器和来自 MarkItDown 包的文件转换工具的 LLM 智能体。它可以在 Magentic-UI 控制的目录中定位文件，将文件转换为 markdown，并回答有关它们的问题。
- 🧑 **用户代理** 是代表与 Magentic-UI 交互的用户的智能体。协调器可以将工作委托给用户而不是其他智能体。

要与 Magentic-UI 交互，**用户可以输入文本消息并附加图像**。作为响应，Magentic-UI 创建一个自然语言的逐步计划，用户可以通过计划编辑界面与之交互。**用户可以添加、删除、编辑、重新生成步骤，并编写后续消息来迭代计划。** 虽然用户编辑计划会增加交互的前期成本，但它可能会在智能体执行计划时节省大量时间并增加成功的机会。

计划存储在协调器内部，用于执行任务。**对于计划的每个步骤，协调器确定哪个智能体（网络冲浪者、编码器、文件冲浪者）或用户应该完成该步骤。** 一旦做出决定，协调器向其中一个智能体或用户发送请求并等待响应。收到响应后，协调器决定该步骤是否完成。如果步骤完成，协调器继续执行下一步。

**一旦所有步骤完成，协调器生成一个最终答案呈现给用户。** 如果在执行任何步骤时，协调器决定计划不充分（例如，因为某个网站无法访问），协调器可以在用户许可下重新规划并执行新计划。

所有中间进度步骤都清楚地显示给用户。此外，用户可以暂停计划的执行并发送额外的请求或反馈。用户还可以通过界面配置智能体操作（例如，点击按钮）是否需要批准。


### 自主评估

为了评估其自主能力，Magentic-UI 在使用 o4-mini 运行时已经针对几个基准进行了测试：[GAIA](https://huggingface.co/datasets/gaia-benchmark/GAIA) 测试集（42.52%），评估通用 AI 助手在推理、工具使用和网络交互任务方面的能力；[AssistantBench](https://huggingface.co/AssistantBench) 测试集（27.60%），专注于现实的、耗时的网络任务；[WebVoyager](https://github.com/MinorJerry/WebVoyager)（82.2%），测量真实世界场景中的端到端网络导航；以及 [WebGames](https://webgames.convergence.ai/)（45.5%），通过交互式挑战评估通用网络浏览智能体。
要重现这些实验结果，请参见以下[说明](experiments/eval/README.md)。



如果您有兴趣阅读更多内容，请查看我们的[博客文章](https://www.microsoft.com/en-us/research/blog/magentic-ui-an-experimental-human-centered-web-agent/)。

## 🛠️ 安装

### 📝 先决条件

**注意**：如果您使用的是 Windows，我们强烈建议使用 [WSL2](https://docs.microsoft.com/en-us/windows/wsl/install)（Windows 子系统 for Linux）。

1. 如果在 **Windows** 或 **Mac** 上运行，您应该使用 [Docker Desktop](https://www.docker.com/products/docker-desktop/)，或者如果在 WSL2 内部，您可以直接在 WSL 内部安装 Docker [WSL2 中的 docker 指南](https://gist.github.com/dehsilvadeveloper/c3bdf0f4cdcc5c177e2fe9be671820c7)。如果在 **Linux** 上运行，您应该使用 [Docker Engine](https://docs.docker.com/engine/install/)。

如果使用 Docker Desktop，请确保它设置为使用 WSL2：
    - 转到设置 > 资源 > WSL 集成
    - 启用与您的开发发行版的集成 您可以在[这里](https://docs.microsoft.com/en-us/windows/wsl/tutorials/wsl-containers)找到关于此步骤的更详细说明。



2. 在安装步骤中，您需要设置您的 `OPENAI_API_KEY`。要使用其他模型，请查看下面的[自定义客户端配置](#自定义客户端配置)部分。

3. 您需要至少安装 [Python 3.10](https://www.python.org/downloads/)。


如果您使用的是 Windows，我们建议在 [WSL2](https://docs.microsoft.com/en-us/windows/wsl/install)（Windows 子系统 for Linux）内运行 Magentic-UI，以确保正确的 Docker 和文件路径兼容性。



### PyPI 安装

Magentic-UI 在 PyPI 上可用。我们建议使用虚拟环境以避免与其他包的冲突。

```bash
python3 -m venv .venv
source .venv/bin/activate
pip install magentic-ui
```

或者，如果您使用 [`uv`](https://docs.astral.sh/uv/getting-started/installation/) 进行依赖管理，您可以使用以下命令安装 Magentic-UI：

```bash
uv venv --python=3.12 .venv
. .venv/bin/activate
uv pip install magentic-ui
```


### 运行 Magentic-UI

要运行 Magentic-UI，请确保 Docker 正在运行，然后运行以下命令：

```bash
magentic-ui --port 8081
```

第一次运行此命令时，构建 Docker 镜像需要一段时间——去喝杯咖啡或做点别的。下次运行时会快得多，因为不需要再次构建 Docker。

如果您在构建 docker 时遇到问题，请尝试使用以下命令重新构建它们：
```bash
magentic-ui --rebuild-docker --port 8081
```
如果您遇到进一步的问题，请参考 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) 文档。

服务器运行后，您可以在 <http://localhost:8081> 访问 UI。


您也可以使用以下命令运行 Magentic-UI 的命令行界面（CLI）：

```bash
magentic-cli --work-dir PATH_TO_STORE_LOGS
```

### 配置

#### 模型客户端配置

如果您想使用不同的 OpenAI 密钥，或者如果您想配置使用 Azure OpenAI 或 Ollama，您可以在 UI 内通过导航到设置（右上角图标）并使用下面 `config.yaml` 文件格式更改模型配置来完成。您也可以创建一个 `config.yaml` 并在 UI 内导入它，或在启动时将 Magentic-UI 指向其路径：
```bash
magentic-ui --config path/to/config.yaml
```

下面给出了 OpenAI 的示例 `config.yaml`：

```yaml
# config.yaml

######################################
# 默认 OpenAI 模型配置 #
######################################
model_config: &client
  provider: autogen_ext.models.openai.OpenAIChatCompletionClient
  config:
    model: gpt-4o
    api_key: <YOUR API KEY>
    max_retries: 10

##########################
# 每个智能体的客户端 #
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client
```

Azure OpenAI 的相应配置是：

```yaml
# config.yaml

######################################
# Azure 模型配置          #
######################################
model_config: &client
  provider: AzureOpenAIChatCompletionClient
  config:
    model: gpt-4o
    azure_endpoint: "<YOUR ENDPOINT>"
    azure_deployment: "<YOUR DEPLOYMENT>"
    api_version: "2024-10-21"
    azure_ad_token_provider:
      provider: autogen_ext.auth.azure.AzureTokenProvider
      config:
        provider_kind: DefaultAzureCredential
        scopes:
          - https://cognitiveservices.azure.com/.default
    max_retries: 10

##########################
# 每个智能体的客户端 #
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client
```

#### MCP 服务器配置

您还可以通过向多智能体团队添加自定义"McpAgents"来扩展 Magentic-UI 的功能。每个 McpAgent 可以访问一个或多个 MCP 服务器。您可以通过 `config.yaml` 中的 `mcp_agent_configs` 参数指定这些智能体。

例如，这里有一个名为"airbnb_surfer"的智能体，它可以通过 Stdio 访问本地运行的 OpenBnb MCP 服务器。

```yaml
mcp_agent_configs:
  - name: airbnb_surfer
    description: "airbnb_surfer 可以直接访问 AirBnB。"
    model_client:
      provider: OpenAIChatCompletionClient
      config:
        model: gpt-4.1-2025-04-14
      max_retries: 10
    system_message: |-
      您是 AirBnb 冲浪者，一个有用的数字助手，可以帮助用户访问 AirBnB。

      您可以访问 AirBnB API 提供的一套工具。使用这些工具来满足用户的请求。
    reflect_on_tool_use: false
    mcp_servers:
      - server_name: AirBnB
        server_params:
          type: StdioServerParams
          command: npx
          args:
            - -y
            - "@openbnb/mcp-server-airbnb"
            - --ignore-robots-txt
```

在底层，每个 `McpAgent` 只是一个 `autogen_agentchat.agents.AssistantAgent`，其中一组 MCP 服务器作为 `AggregateMcpWorkbench` 公开，这只是 `autogen_ext.tools.mcp.McpWorkbench` 对象的命名集合（每个 MCP 服务器一个）。

目前支持的 MCP 服务器类型是 `autogen_ext.tools.mcp.StdioServerParams` 和 `autogen_ext.tools.mcp.SseServerParams`。

### 从源代码构建 Magentic-UI

此步骤主要适用于寻求修改代码、在 pypi 安装时遇到问题或希望在 pypi 版本发布之前获得最新代码的用户。

#### 1. 确保安装了上述先决条件，并且 Docker 正在运行。

#### 2. 将存储库克隆到您的本地机器：

```bash
git clone https://github.com/microsoft/magentic-ui.git
cd magentic-ui
```

#### 3. 使用 uv 安装 Magentic-UI 的依赖项：

```bash
# 通过 https://docs.astral.sh/uv/getting-started/installation/ 安装 uv
uv venv --python=3.12 .venv
uv sync --all-extras
source .venv/bin/activate
```

#### 4. 构建前端：

首先确保安装 node：

```bash
# 安装 nvm 来安装 node
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
nvm install node
```

然后安装前端：

```bash
cd frontend
npm install -g gatsby-cli
npm install --global yarn
yarn install
yarn build
```

#### 5. 像往常一样运行 Magentic-UI。

```bash
magentic-ui --port 8081
```

>**注意**：第一次运行此命令将构建 Magentic-UI 智能体所需的两个 docker 容器。如果遇到问题，您可以从存储库内部使用以下命令直接构建它们：
```bash
docker build -t magentic-ui-vnc-browser:latest ./src/magentic_ui/docker/magentic-ui-browser-docker

docker build -t magentic-ui-python-env:latest ./src/magentic_ui/docker/magentic-ui-python-env
```

#### 从源代码运行 UI

如果您正在对 UI 的源代码进行更改，您可以在开发模式下运行前端，这样当您进行更改时它会自动更新，以便更快地开发。

1. 打开一个单独的终端并切换到前端目录

```bash
cd frontend
```

2. 创建一个 `.env.development` 文件。

```bash
cp .env.default .env.development
```

3. 启动前端服务器

```bash
npm run start
```

4. 然后运行 UI：

```bash
magentic-ui --port 8081
```

源代码的前端将在 <http://localhost:8000> 可用，编译的前端将在 <http://localhost:8081> 可用。


## ⚠️ 故障排除

如果您无法让 Magentic-UI 运行，不要担心！第一步是确保您已经遵循了上述步骤，特别是[先决条件](#先决条件)。

对于常见问题及其解决方案，请参考此存储库中的 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) 文件。如果您在那里没有看到您的问题，请打开一个 `GitHub Issue`。

## 🤝 贡献

此项目欢迎贡献和建议。有关为 Magentic-UI 做贡献的信息，请参阅我们的 [CONTRIBUTING.md](CONTRIBUTING.md) 指南，其中包括要解决的当前问题和其他贡献形式。

此项目已采用 [Microsoft 开源行为准则](https://opensource.microsoft.com/codeofconduct/)。有关更多信息，请参阅[行为准则常见问题解答](https://opensource.microsoft.com/codeofconduct/faq/)或联系 [<EMAIL>](mailto:<EMAIL>) 提出任何其他问题或意见。

## 📄 许可证

Microsoft 和任何贡献者根据 [MIT 许可证](https://opensource.org/licenses/MIT) 向您授予存储库中任何代码的许可证。请参阅 [LICENSE](LICENSE) 文件。

Microsoft、Windows、Microsoft Azure 和/或文档中引用的其他 Microsoft 产品和服务可能是 Microsoft 在美国和/或其他国家的商标或注册商标。
此项目的许可证不授予您使用任何 Microsoft 名称、徽标或商标的权利。
Microsoft 的一般商标指南可以在 <http://go.microsoft.com/fwlink/?LinkID=254653> 找到。

任何第三方商标或徽标的使用都受这些第三方政策的约束。

隐私信息可以在 <https://go.microsoft.com/fwlink/?LinkId=521839> 找到

Microsoft 和任何贡献者保留所有其他权利，无论是在其各自的版权、专利或商标下，无论是通过暗示、禁止反言还是其他方式。
