# Magentic-UI Configuration File
# Please set your OpenAI API key here or as an environment variable

model_client_configs:
  default: &default_client
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o
      api_key: "your-openai-api-key-here"  # Replace with your actual API key
    max_retries: 10

  orchestrator: *default_client
  web_surfer: *default_client
  coder: *default_client
  file_surfer: *default_client

  action_guard:
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o-mini
      api_key: "your-openai-api-key-here"  # Replace with your actual API key
    max_retries: 10

# Other configuration options
logs_dir: "./logs"
