# Magentic-UI Configuration File
# Please set your OpenAI API key here or as an environment variable

model_client_configs:
  orchestrator:
    component_type: "autogen_ext.models.openai.OpenAIChatCompletionClient"
    model: "gpt-4o"
    api_key: "your-openai-api-key-here"  # Replace with your actual API key

  coder:
    component_type: "autogen_ext.models.openai.OpenAIChatCompletionClient"
    model: "gpt-4o"
    api_key: "your-openai-api-key-here"  # Replace with your actual API key

  web_surfer:
    component_type: "autogen_ext.models.openai.OpenAIChatCompletionClient"
    model: "gpt-4o"
    api_key: "your-openai-api-key-here"  # Replace with your actual API key

  file_surfer:
    component_type: "autogen_ext.models.openai.OpenAIChatCompletionClient"
    model: "gpt-4o"
    api_key: "your-openai-api-key-here"  # Replace with your actual API key

# Other configuration options
logs_dir: "./logs"
