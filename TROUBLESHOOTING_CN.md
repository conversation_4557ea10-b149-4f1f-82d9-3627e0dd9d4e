# Magentic-UI 故障排除指南

## 🔧 常见问题解决方案

### 1. URL 路径重复错误
**错误信息**: `Invalid URL (POST /v1/chat/completions/chat/completions)`

**原因**: `base_url` 配置不正确，包含了多余的路径

**解决方案**:
```yaml
# 错误配置
base_url: "https://api.openai-hk.com/v1/"

# 正确配置
base_url: "https://api.openai-hk.com"
```

### 2. API 密钥错误
**错误信息**: `The api_key client option must be set`

**解决方案**:
```powershell
# 设置环境变量
$env:OPENAI_API_KEY="your-api-key-here"
```

### 3. 前端 UI "Not Found" 错误
**原因**: 前端文件缺失

**解决方案**:
```powershell
cd frontend
npm install --legacy-peer-deps
npm run build
xcopy /E /I /Y public ..\src\magentic_ui\backend\web\ui
cd ..
```

### 4. Playwright 浏览器缺失
**错误信息**: `Executable doesn't exist at ...chrome.exe`

**解决方案**:
```powershell
playwright install
```

### 5. Docker 相关问题
**解决方案**:
```powershell
# 检查 Docker 状态
docker --version

# 重启 Docker Desktop
# 或使用 --run-without-docker 选项
magentic-ui --run-without-docker --port 8082
```

## 🚀 快速启动

### 方法 1: 使用启动脚本
```powershell
.\start_magentic_ui.ps1
```

### 方法 2: 手动启动
```powershell
# 设置环境变量
$env:OPENAI_API_KEY="hk-a81cro100001220955e04b42c93c04f8e9b16f62aa15c8d8"
$env:OPENAI_BASE_URL="https://api.openai-hk.com"

# 启动服务
magentic-ui --config ./simple_config.yaml --port 8082
```

## 📋 配置文件示例

### OpenAI-HK 配置
```yaml
model_client_configs:
  default: &default_client
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o
      base_url: "https://api.openai-hk.com"
    max_retries: 10
  
  orchestrator: *default_client
  web_surfer: *default_client
  coder: *default_client
  file_surfer: *default_client
```

### 标准 OpenAI 配置
```yaml
model_client_configs:
  default: &default_client
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o
      # base_url 留空使用默认 OpenAI API
    max_retries: 10
```

## 🔍 调试技巧

### 检查环境变量
```powershell
echo $env:OPENAI_API_KEY
echo $env:OPENAI_BASE_URL
```

### 查看日志
日志文件位置: `C:\Users\<USER>\.magentic_ui\logs\`

### 测试 API 连接
```powershell
python test_api_key.py
```

## 📞 获取帮助

如果遇到其他问题，请检查:
1. 网络连接是否正常
2. API 密钥是否有效
3. 防火墙是否阻止了端口 8082
4. Docker Desktop 是否正在运行
