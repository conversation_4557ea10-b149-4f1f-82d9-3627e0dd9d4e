# Simple Magentic-UI Configuration File
# Uses environment variables for API keys

model_client_configs:
  default: &default_client
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o
      base_url: "https://api.openai-hk.com"
    max_retries: 10
  
  orchestrator: *default_client
  web_surfer: *default_client
  coder: *default_client
  file_surfer: *default_client
  
  action_guard:
    provider: OpenAIChatCompletionClient
    config:
      model: gpt-4o-mini
    max_retries: 10

# Basic settings
cooperative_planning: true
autonomous_execution: false
max_actions_per_step: 5
max_turns: 20
approval_policy: "auto-conservative"
