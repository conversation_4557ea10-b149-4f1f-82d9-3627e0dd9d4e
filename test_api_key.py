#!/usr/bin/env python3
"""
Simple script to test OpenAI API key configuration
"""
import os
import sys
from openai import OpenAI

def test_api_key():
    """Test if OpenAI API key is properly configured"""
    
    # Check environment variable
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("❌ OPENAI_API_KEY environment variable is not set")
        print("Please set it using: $env:OPENAI_API_KEY='your-api-key-here'")
        return False
    
    if not api_key.startswith('sk-'):
        print("❌ API key format appears invalid (should start with 'sk-')")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Test API connection (without making actual calls)
    try:
        client = OpenAI(api_key=api_key)
        print("✅ OpenAI client initialized successfully")
        print("🎉 Configuration appears correct!")
        return True
    except Exception as e:
        print(f"❌ Error initializing OpenAI client: {e}")
        return False

if __name__ == "__main__":
    print("🔑 Testing OpenAI API Key Configuration...")
    print("=" * 50)
    
    success = test_api_key()
    
    if success:
        print("\n✅ You can now run Magentic-UI!")
        print("Run: magentic-ui --port 8082")
    else:
        print("\n❌ Please fix the API key configuration first")
        sys.exit(1)
